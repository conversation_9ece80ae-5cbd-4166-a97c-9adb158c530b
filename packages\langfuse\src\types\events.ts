/**
 * Events emitted by the Langfuse service
 */
export interface LangfuseEvents {
	/** Emitted when a trace is created */
	traceCreated: [traceId: string, metadata?: Record<string, unknown>]

	/** Emitted when a trace is completed */
	traceCompleted: [traceId: string, metadata?: Record<string, unknown>]

	/** Emitted when an LLM call is traced */
	llmCallTraced: [callId: string, metadata?: Record<string, unknown>]

	/** Emitted when a conversation is traced */
	conversationTraced: [conversationId: string, metadata?: Record<string, unknown>]

	/** Emitted when tool usage is traced */
	toolUsageTraced: [toolId: string, metadata?: Record<string, unknown>]

	/** Emitted when an error occurs */
	error: [error: Error, context?: string]

	/** Emitted when configuration changes */
	configChanged: [config: Record<string, unknown>]
}

/**
 * Langfuse service status
 */
export enum LangfuseStatus {
	DISABLED = "disabled",
	INITIALIZING = "initializing",
	READY = "ready",
	ERROR = "error",
}

/**
 * Trace types supported by the service
 */
export enum TraceType {
	LLM_CALL = "llm_call",
	CONVERSATION = "conversation",
	TOOL_USAGE = "tool_usage",
	TASK = "task",
	SESSION = "session",
}

/**
 * Error types that can occur in Langfuse integration
 */
export enum LangfuseErrorType {
	CONFIGURATION_ERROR = "configuration_error",
	API_ERROR = "api_error",
	NETWORK_ERROR = "network_error",
	VALIDATION_ERROR = "validation_error",
	UNKNOWN_ERROR = "unknown_error",
}
