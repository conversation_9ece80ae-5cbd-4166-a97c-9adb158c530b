import type { Anthropic } from "@anthropic-ai/sdk"
import type {
	ModelInfo,
	ProviderSettings,
} from "@roo-code/types"
import type {
	A<PERSON><PERSON><PERSON><PERSON>,
	ApiHandlerCreateMessageMetadata,
	ApiStream,
	ApiStreamChunk,
} from "./types"

import { LangfuseService } from "./LangfuseService"
import type { LlmCallTrace, LangfuseTraceMetadata } from "./types"
import { generateTraceId, formatAnthropicMessages, formatError } from "./utils/formatters"

/**
 * Middleware that wraps API handlers to provide Langfuse tracing
 * This allows non-intrusive integration with existing LLM providers
 */
export class LangfuseMiddleware implements ApiHandler {
	private originalHandler: ApiHandler
	private langfuseService: LangfuseService | null
	private providerName: string

	constructor(originalHandler: ApiHandler, providerName: string) {
		this.originalHandler = originalHandler
		this.providerName = providerName
		this.langfuseService = LangfuseService.getInstance()
	}

	/**
	 * Wraps the createMessage method to add Langfuse tracing
	 */
	async *createMessage(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		metadata?: ApiHandlerCreateMessageMetadata,
	): ApiStream {
		const traceId = generateTraceId()
		const startTime = new Date()
		const model = this.getModel()

		// Create initial trace data
		const llmTrace: LlmCallTrace = {
			id: traceId,
			model: model.id,
			provider: this.providerName,
			input: {
				messages: formatAnthropicMessages(messages),
				systemPrompt,
				maxTokens: model.info.maxTokens ?? undefined,
			},
			startTime,
			metadata: {
				provider: this.providerName,
				modelInfo: model.info,
				...metadata,
			},
		}

		let outputContent = ""
		let usage: { inputTokens: number; outputTokens: number; totalTokens: number; cost?: number } | undefined
		let error: string | undefined

		try {
			// Call the original handler and intercept the stream
			const originalStream = this.originalHandler.createMessage(systemPrompt, messages, metadata)

			for await (const chunk of originalStream) {
				// Forward the chunk to the caller
				yield chunk

				// Collect data for tracing
				if (chunk.type === "text") {
					outputContent += chunk.text
				} else if (chunk.type === "usage") {
					usage = {
						inputTokens: chunk.inputTokens,
						outputTokens: chunk.outputTokens,
						totalTokens: chunk.inputTokens + chunk.outputTokens,
						cost: chunk.totalCost,
					}
				}
			}

			// Complete the trace with success
			llmTrace.endTime = new Date()
			llmTrace.output = {
				content: outputContent,
				finishReason: "completed",
			}
			llmTrace.usage = usage
		} catch (err) {
			// Complete the trace with error
			error = formatError(err)
			llmTrace.endTime = new Date()
			llmTrace.error = error

			// Re-throw the error to maintain original behavior
			throw err
		} finally {
			// Send trace to Langfuse (fire and forget to avoid affecting performance)
			this.sendTraceAsync(llmTrace, metadata)
		}
	}

	/**
	 * Delegates to the original handler
	 */
	getModel(): { id: string; info: ModelInfo } {
		return this.originalHandler.getModel()
	}

	/**
	 * Delegates to the original handler
	 */
	async countTokens(content: Array<Anthropic.Messages.ContentBlock>): Promise<number> {
		return this.originalHandler.countTokens(content)
	}

	/**
	 * Sends trace to Langfuse asynchronously to avoid blocking the main flow
	 */
	private sendTraceAsync(trace: LlmCallTrace, metadata?: ApiHandlerCreateMessageMetadata): void {
		if (!this.langfuseService?.isReady()) {
			return
		}

		// Use setTimeout to make this truly async and non-blocking
		setTimeout(async () => {
			try {
				const traceMetadata: LangfuseTraceMetadata = {
					taskId: metadata?.taskId,
					tags: ["llm", "api-call", this.providerName],
					metadata: {
						provider: this.providerName,
						...metadata,
					},
				}

				await this.langfuseService!.traceLlmCall(trace, traceMetadata)
			} catch (error) {
				// Log error but don't throw to avoid affecting the main application
				console.error(`[LangfuseMiddleware] Failed to send trace: ${error}`)
			}
		}, 0)
	}

	/**
	 * Creates a middleware wrapper for any API handler
	 */
	static wrap(handler: ApiHandler, providerName: string): ApiHandler {
		return new LangfuseMiddleware(handler, providerName)
	}

	/**
	 * Wraps multiple handlers with middleware
	 */
	static wrapHandlers(handlers: Record<string, ApiHandler>): Record<string, ApiHandler> {
		const wrappedHandlers: Record<string, ApiHandler> = {}

		for (const [name, handler] of Object.entries(handlers)) {
			wrappedHandlers[name] = LangfuseMiddleware.wrap(handler, name)
		}

		return wrappedHandlers
	}

	/**
	 * Conditionally wraps a handler only if Langfuse is enabled
	 */
	static conditionalWrap(handler: ApiHandler, providerName: string): ApiHandler {
		const langfuseService = LangfuseService.getInstance()

		if (langfuseService?.isReady()) {
			return LangfuseMiddleware.wrap(handler, providerName)
		}

		return handler
	}
}

/**
 * Utility function to wrap the buildApiHandler function from the main API
 */
export function wrapApiHandlerBuilder(
	originalBuilder: (config: ProviderSettings) => ApiHandler,
): (config: ProviderSettings) => ApiHandler {
	return (config: ProviderSettings) => {
		const handler = originalBuilder(config)
		const providerName = config.apiProvider || "unknown"
		return LangfuseMiddleware.conditionalWrap(handler, providerName)
	}
}

/**
 * Enhanced API stream that includes tracing metadata
 */
export interface TracedApiStream extends ApiStream {
	traceId?: string
}

/**
 * Enhanced API stream chunk that includes tracing information
 */
export type TracedApiStreamChunk = ApiStreamChunk & {
	traceId?: string
	timestamp?: Date
}

/**
 * Configuration for the middleware
 */
export interface LangfuseMiddlewareConfig {
	/** Whether to trace all API calls */
	traceApiCalls: boolean

	/** Whether to include request/response content */
	includeContent: boolean

	/** Maximum content length to trace */
	maxContentLength: number

	/** Whether to trace errors */
	traceErrors: boolean

	/** Custom tags to add to all traces */
	defaultTags: string[]
}

/**
 * Default middleware configuration
 */
export const DEFAULT_MIDDLEWARE_CONFIG: LangfuseMiddlewareConfig = {
	traceApiCalls: true,
	includeContent: true,
	maxContentLength: 10000,
	traceErrors: true,
	defaultTags: ["api", "middleware"],
}
