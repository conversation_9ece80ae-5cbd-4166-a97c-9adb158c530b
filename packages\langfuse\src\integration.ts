/**
 * Integration utilities for connecting Lang<PERSON> with the existing Roo Code API system
 * This file provides examples and utilities for integrating Langfuse tracing
 * without modifying the core API infrastructure
 */

import type { ProviderSettings } from "@roo-code/types"
import type { ApiHand<PERSON> } from "./types"
import type { TelemetryService } from "@roo-code/telemetry"
import type * as vscode from "vscode"
import { LangfuseMiddleware, wrapApiHandlerBuilder } from "./LangfuseMiddleware"
import { LangfuseService } from "./LangfuseService"
import type { LangfuseConfig } from "./types"

/**
 * Example: How to wrap the existing buildApiHandler function
 * This can be used in src/api/index.ts to add Langfuse tracing
 */
export function createLangfuseEnabledApiBuilder(
	originalBuildApiHandler: (config: ProviderSettings) => ApiHandler,
): (config: ProviderSettings) => ApiHandler {
	return wrapApiHandlerBuilder(originalBuildApiHandler)
}

/**
 * Example: How to initialize <PERSON><PERSON> in the extension activation
 * This should be called in src/extension.ts during activation
 */
export async function initializeLangfuseIntegration(config: LangfuseConfig): Promise<LangfuseService | null> {
	try {
		const langfuseService = await LangfuseService.createInstance(config)

		if (langfuseService.isReady()) {
			console.log("[Langfuse] Integration initialized successfully")
			return langfuseService
		} else {
			console.log("[Langfuse] Integration disabled or not configured")
			return null
		}
	} catch (error) {
		console.error("[Langfuse] Failed to initialize integration:", error)
		return null
	}
}

/**
 * Example: How to register Langfuse as a telemetry client
 * This should be called after TelemetryService.createInstance()
 */
export function registerLangfuseWithTelemetry(
	telemetryService: TelemetryService,
	langfuseService: LangfuseService,
): void {
	try {
		telemetryService.register(langfuseService)
		console.log("[Langfuse] Registered with telemetry service")
	} catch (error) {
		console.error("[Langfuse] Failed to register with telemetry service:", error)
	}
}

/**
 * Example: How to create a configuration from environment variables and settings
 */
export function createLangfuseConfigFromEnvironment(): LangfuseConfig {
	return {
		enabled: process.env.LANGFUSE_ENABLED === "true" || false,
		publicKey: process.env.LANGFUSE_PUBLIC_KEY,
		secretKey: process.env.LANGFUSE_SECRET_KEY,
		baseUrl: process.env.LANGFUSE_BASE_URL,
		traceLlmCalls: true,
		traceConversations: true,
		traceToolUsage: true,
		capturePrompts: true,
		captureResponses: true,
		maxContentLength: parseInt(process.env.LANGFUSE_MAX_CONTENT_LENGTH || "0", 10),
		respectPrivacySettings: true,
		defaultTags: ["roo-code"],
		sessionTimeoutMinutes: 60,
	}
}

/**
 * Example: How to create a configuration from VSCode settings
 */
export function createLangfuseConfigFromSettings(settings: vscode.WorkspaceConfiguration): LangfuseConfig {
	return {
		enabled: settings.get("langfuse.enabled", false),
		publicKey: settings.get("langfuse.publicKey"),
		secretKey: settings.get("langfuse.secretKey"),
		baseUrl: settings.get("langfuse.baseUrl"),
		traceLlmCalls: settings.get("langfuse.traceLlmCalls", true),
		traceConversations: settings.get("langfuse.traceConversations", true),
		traceToolUsage: settings.get("langfuse.traceToolUsage", true),
		capturePrompts: settings.get("langfuse.capturePrompts", true),
		captureResponses: settings.get("langfuse.captureResponses", true),
		maxContentLength: settings.get("langfuse.maxContentLength", 0),
		respectPrivacySettings: settings.get("langfuse.respectPrivacySettings", true),
		defaultTags: settings.get("langfuse.defaultTags", ["roo-code"]),
		sessionTimeoutMinutes: settings.get("langfuse.sessionTimeoutMinutes", 60),
	}
}

/**
 * Example: How to manually wrap specific API handlers
 */
export function wrapSpecificHandlers(handlers: {
	anthropic?: ApiHandler
	openai?: ApiHandler
	[key: string]: ApiHandler | undefined
}): Record<string, ApiHandler> {
	const wrappedHandlers: Record<string, ApiHandler> = {}

	for (const [name, handler] of Object.entries(handlers)) {
		if (handler) {
			wrappedHandlers[name] = LangfuseMiddleware.conditionalWrap(handler, name)
		}
	}

	return wrappedHandlers
}

/**
 * Example: How to add task-specific tracing
 */
export async function traceTaskStart(taskId: string, initialMessage?: string): Promise<void> {
	const langfuseService = LangfuseService.getInstance()
	if (!langfuseService?.isReady()) {
		return
	}

	await langfuseService.startSession(taskId, {
		taskId,
		tags: ["task", "start"],
		metadata: {
			initialMessage,
			timestamp: new Date().toISOString(),
		},
	})
}

/**
 * Example: How to add task-specific tracing for completion
 */
export async function traceTaskEnd(taskId: string, result?: string): Promise<void> {
	const langfuseService = LangfuseService.getInstance()
	if (!langfuseService?.isReady()) {
		return
	}

	await langfuseService.endSession(taskId, {
		result,
		timestamp: new Date().toISOString(),
	})
}

/**
 * Example: How to trace tool usage manually
 */
export async function traceToolUsage(
	taskId: string,
	toolName: string,
	input: unknown,
	output?: unknown,
	error?: string,
): Promise<void> {
	const langfuseService = LangfuseService.getInstance()
	if (!langfuseService?.isReady()) {
		return
	}

	await langfuseService.traceToolUsage(
		{
			id: `tool_${Date.now()}_${Math.random().toString(36).substring(2)}`,
			toolName,
			input,
			output,
			startTime: new Date(),
			endTime: new Date(),
			error,
		},
		{
			taskId,
			tags: ["tool", toolName],
		},
	)
}

/**
 * Utility to check if Langfuse is available and ready
 */
export function isLangfuseReady(): boolean {
	const langfuseService = LangfuseService.getInstance()
	return langfuseService?.isReady() || false
}

/**
 * Utility to get Langfuse service status
 */
export function getLangfuseStatus(): string {
	const langfuseService = LangfuseService.getInstance()
	return langfuseService?.getStatus() || "not_initialized"
}

/**
 * Utility to flush pending traces (useful before shutdown)
 */
export async function flushLangfuseTraces(): Promise<void> {
	const langfuseService = LangfuseService.getInstance()
	if (langfuseService?.isReady()) {
		await langfuseService.flush()
	}
}
