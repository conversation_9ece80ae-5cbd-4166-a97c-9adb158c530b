import type { Anthropic } from "@anthropic-ai/sdk"
import type { LlmCallTrace, ConversationTrace, ToolUsageTrace } from "../types"

/**
 * Formats Anthropic messages for Langfuse tracing
 */
export function formatAnthropicMessages(messages: Anthropic.Messages.MessageParam[]): unknown[] {
	return messages.map((message) => ({
		role: message.role,
		content:
			typeof message.content === "string"
				? message.content
				: Array.isArray(message.content)
					? message.content.map(formatContentBlock)
					: message.content,
	}))
}

/**
 * Formats content blocks for tracing
 */
function formatContentBlock(block: Anthropic.Messages.TextBlockParam | Anthropic.Messages.ImageBlockParam | Anthropic.Messages.ToolUseBlockParam | Anthropic.Messages.ToolResultBlockParam): unknown {
	if (block.type === "text") {
		return {
			type: "text",
			text: block.text,
		}
	}

	if (block.type === "image") {
		return {
			type: "image",
			source: {
				type: block.source.type,
				// Don't include actual image data for privacy/size reasons
				media_type: block.source.media_type,
				data: "[IMAGE_DATA_OMITTED]",
			},
		}
	}

	if (block.type === "tool_use") {
		return {
			type: "tool_use",
			id: block.id,
			name: block.name,
			input: block.input,
		}
	}

	if (block.type === "tool_result") {
		return {
			type: "tool_result",
			tool_use_id: block.tool_use_id,
			content:
				typeof block.content === "string"
					? block.content
					: Array.isArray(block.content)
						? block.content.map(formatContentBlock)
						: block.content,
			is_error: block.is_error,
		}
	}

	return block
}

/**
 * Truncates content if it exceeds the maximum length
 */
export function truncateContent(content: string, maxLength: number): string {
	if (maxLength === 0 || content.length <= maxLength) {
		return content
	}

	return content.substring(0, maxLength) + "... [TRUNCATED]"
}

/**
 * Sanitizes metadata by removing sensitive information
 */
export function sanitizeMetadata(metadata: Record<string, unknown>): Record<string, unknown> {
	const sanitized = { ...metadata }

	// Remove potentially sensitive keys
	const sensitiveKeys = ["apiKey", "secretKey", "password", "token", "auth", "authorization"]

	for (const key of sensitiveKeys) {
		if (key in sanitized) {
			sanitized[key] = "[REDACTED]"
		}
	}

	return sanitized
}

/**
 * Generates a unique trace ID
 */
export function generateTraceId(): string {
	return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
}

/**
 * Formats error for tracing
 */
export function formatError(error: unknown): string {
	if (error instanceof Error) {
		return `${error.name}: ${error.message}`
	}

	if (typeof error === "string") {
		return error
	}

	return String(error)
}

/**
 * Calculates duration between two dates in milliseconds
 */
export function calculateDuration(startTime: Date, endTime: Date): number {
	return endTime.getTime() - startTime.getTime()
}

/**
 * Formats LLM call trace for Langfuse
 */
export function formatLlmCallForLangfuse(trace: LlmCallTrace) {
	return {
		id: trace.id,
		name: `${trace.provider}:${trace.model}`,
		input: trace.input,
		output: trace.output,
		metadata: {
			provider: trace.provider,
			model: trace.model,
			...sanitizeMetadata(trace.metadata || {}),
		},
		usage: trace.usage,
		startTime: trace.startTime,
		endTime: trace.endTime,
		level: trace.error ? "ERROR" : "DEFAULT",
		statusMessage: trace.error,
	}
}

/**
 * Formats conversation trace for Langfuse
 */
export function formatConversationForLangfuse(trace: ConversationTrace) {
	return {
		id: trace.id,
		name: `conversation:${trace.taskId}`,
		input: {
			messages: trace.messages.map((msg) => ({
				role: msg.role,
				content: msg.content,
				timestamp: msg.timestamp,
			})),
		},
		metadata: {
			taskId: trace.taskId,
			messageCount: trace.messages.length,
			...sanitizeMetadata(trace.metadata || {}),
		},
		startTime: trace.startTime,
		endTime: trace.endTime,
	}
}

/**
 * Formats tool usage trace for Langfuse
 */
export function formatToolUsageForLangfuse(trace: ToolUsageTrace) {
	return {
		id: trace.id,
		name: `tool:${trace.toolName}`,
		input: trace.input,
		output: trace.output,
		metadata: {
			toolName: trace.toolName,
			...sanitizeMetadata(trace.metadata || {}),
		},
		startTime: trace.startTime,
		endTime: trace.endTime,
		level: trace.error ? "ERROR" : "DEFAULT",
		statusMessage: trace.error,
	}
}
